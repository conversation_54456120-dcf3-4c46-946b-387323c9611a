<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug: Log all POST data
    error_log('POST DATA: ' . print_r($_POST, true));

    $id = isset($_POST['id_jurusan']) ? $_POST['id_jurusan'] : null;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'ID Jurusan tidak ditemukan.']);
        $conn->close();
        exit;
    }

    // If update flag is set, perform update
    if (isset($_POST['update'])) {
        $kode = isset($_POST['kode_jurusan']) ? $_POST['kode_jurusan'] : '';
        $nama = isset($_POST['nama_jurusan']) ? $_POST['nama_jurusan'] : '';

        $sql = 'UPDATE jurusan SET kode_jurusan=?, nama_jurusan=? WHERE id_jurusan=?';
        error_log('SQL: ' . $sql);
        error_log('PARAMS: ' . print_r([$kode, $nama, $id], true));

        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sss', $kode, $nama, $id);

        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Data jurusan berhasil diperbarui.']);
        } else {
            error_log('MYSQL ERROR: ' . $stmt->error);
            echo json_encode(['success' => false, 'message' => 'Gagal memperbarui data jurusan.', 'error' => $stmt->error]);
        }
        $stmt->close();
    } else {
        // Otherwise, return current data
        $stmt = $conn->prepare('SELECT id_jurusan, kode_jurusan, nama_jurusan FROM jurusan WHERE id_jurusan = ?');
        $stmt->bind_param('s', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $jurusan = $result && $row = $result->fetch_assoc() ? $row : null;
        $stmt->close();

        if ($jurusan) {
            echo json_encode(['success' => true, 'data' => $jurusan]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Data jurusan tidak ditemukan.']);
        }
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
