CREATE TABLE IF NOT EXISTS jurusan (
    id_jurusan VARCHAR(255) PRIMARY KEY,
    kode_jurusan VARCHAR(255) NOT NULL,
    nama_jurusan VARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS prodi (
    id_prodi VARCHAR(255) PRIMARY KEY,
    kode_prodi VARCHAR(255) NOT NULL,
    id_jurusan VARCHAR(255) NOT NULL,
    nama_prodi VARCHAR(255) NOT NULL,
    FOREIGN KEY (id_jurusan) REFERENCES jurusan(id_jurusan)
);

CREATE TABLE IF NOT EXISTS mahasiswa (
    nim_mahasiswa VARCHAR(255) PRIMARY KEY,
    nama_mahasiswa VARCHAR(255) NOT NULL,
    prodi_mahasiswa VARCHAR(255) NOT NULL,
    jurusan_mahasiswa VARCHAR(255) NOT NULL,
    email_mahasiswa VARCHAR(255) UNIQUE NOT NULL,
    password_mahasiswa VARCHAR(255) NOT NULL,
    foto_mahasiswa VARCHAR(255) NOT NULL,
    FOREIGN KEY (prodi_mahasiswa) REFERENCES prodi(id_prodi),
    FOREIGN KEY (jurusan_mahasiswa) REFERENCES jurusan(id_jurusan)
);

INSERT INTO jurusan (id_jurusan, kode_jurusan, nama_jurusan) VALUES ('J01', 'IT01', 'Teknologi Informasi');

INSERT INTO prodi (id_prodi, kode_prodi, id_jurusan, nama_prodi) VALUES ('P01', 'IT01', 'J01', 'Teknik Informatika');
INSERT INTO prodi (id_prodi, kode_prodi, id_jurusan, nama_prodi) VALUES ('P02', 'IT02', 'J01', 'Teknik Komputer');
INSERT INTO prodi (id_prodi, kode_prodi, id_jurusan, nama_prodi) VALUES ('P03', 'IT03', 'J01', 'Teknik Informatika Multimedia');
INSERT INTO prodi (id_prodi, kode_prodi, id_jurusan, nama_prodi) VALUES ('P04', 'IT04', 'J01', 'Teknik Rekayasa Komputer');

INSERT INTO mahasiswa (nim_mahasiswa, nama_mahasiswa, prodi_mahasiswa, jurusan_mahasiswa, email_mahasiswa, password_mahasiswa, foto_mahasiswa) VALUES ('236151056', 'Rio Septianto', 'P01', 'J01', '<EMAIL>', 'password123', 'images/mahasiswa_picture/default.jpg');
