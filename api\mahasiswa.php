<?php
header('Content-Type: application/json');
include '../database.php';

$sql = "SELECT m.nim_mahasiswa, m.nama_mahas<PERSON>wa, p.nama_prodi, j.nama_jurusan, m.email_mahasiswa, m.foto_mahasiswa FROM mahasiswa m JOIN prodi p ON m.prodi_mahasiswa = p.id_prodi JOIN jurusan j ON m.jurusan_mahasiswa = j.id_jurusan";
$result = $conn->query($sql);

$data = [];
if ($result && $result->num_rows > 0) {
    while($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
}
echo json_encode($data); 