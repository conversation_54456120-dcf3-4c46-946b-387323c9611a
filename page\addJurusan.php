<?php
include 'database.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_jurusan = $_POST['id_jurusan'];
    $kode_jurusan = $_POST['kode_jurusan'];
    $nama_jurusan = $_POST['nama_jurusan'];
    
    // Insert into database
    $sql = 'INSERT INTO jurusan (id_jurusan, kode_jurusan, nama_jurusan) VALUES (?, ?, ?)';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sss', $id_jurusan, $kode_jurusan, $nama_jurusan);
    
    if ($stmt->execute()) {
        $message = 'Data jurusan berhasil ditambahkan!';
        $messageType = 'success';
        // Clear form data
        $id_jurusan = $kode_jurusan = $nama_jurusan = '';
    } else {
        $message = 'Gagal menambahkan data jurusan: ' . $stmt->error;
        $messageType = 'error';
    }
    $stmt->close();
}
?>

<main class="dashboard-main">
    <div class="edit-mahasiswa-card">
        <header><h1 style="color: white;">Tambah Jurusan</h1></header>
        
        <?php if ($message): ?>
            <div class="message <?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <form method="post" class="edit-mahasiswa-form" style="display:flex;flex-direction:column;gap:1.2em;">
            <label>ID Jurusan
                <input type="text" name="id_jurusan" value="<?= htmlspecialchars($id_jurusan ?? '') ?>" required>
            </label>
            
            <label>Kode Jurusan
                <input type="text" name="kode_jurusan" value="<?= htmlspecialchars($kode_jurusan ?? '') ?>" required>
            </label>
            
            <label>Nama Jurusan
                <input type="text" name="nama_jurusan" value="<?= htmlspecialchars($nama_jurusan ?? '') ?>" required>
            </label>
            
            <button type="submit">Tambah Jurusan</button>
            <a href="index.php?page=jurusanPage.php" class="cancel-btn">Batal</a>
        </form>
    </div>
</main>

<style>
.message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
</style>
