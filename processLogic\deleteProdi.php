<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = isset($_POST['id_prodi']) ? $_POST['id_prodi'] : null;
    if ($id) {
        // Check if prodi is being used by any mahasiswa
        $checkStmt = $conn->prepare('SELECT COUNT(*) as count FROM mahasiswa WHERE prodi_mahasiswa = ?');
        $checkStmt->bind_param('s', $id);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $checkRow = $checkResult->fetch_assoc();
        $checkStmt->close();

        if ($checkRow['count'] > 0) {
            echo json_encode(['success' => false, 'message' => 'Tidak dapat menghapus prodi karena masih digunakan oleh mahasiswa.']);
        } else {
            $stmt = $conn->prepare('DELETE FROM prodi WHERE id_prodi = ?');
            $stmt->bind_param('s', $id);
            if ($stmt->execute()) {
                echo json_encode(['success' => true, 'message' => 'Data prodi berhasil dihapus.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Gagal menghapus data prodi.']);
            }
            $stmt->close();
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'ID Prodi tidak ditemukan.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
