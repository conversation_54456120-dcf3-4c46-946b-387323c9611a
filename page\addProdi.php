<?php
include 'database.php';

$jurusanList = [];
$message = '';
$messageType = '';

// Fetch jurusan
$result = $conn->query('SELECT id_jurusan, nama_jurusan FROM jurusan');
while ($row = $result->fetch_assoc()) {
    $jurusanList[] = $row;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_prodi = $_POST['id_prodi'];
    $kode_prodi = $_POST['kode_prodi'];
    $nama_prodi = $_POST['nama_prodi'];
    $id_jurusan = $_POST['id_jurusan'];
    
    // Insert into database
    $sql = 'INSERT INTO prodi (id_prodi, kode_prodi, nama_prodi, id_jurusan) VALUES (?, ?, ?, ?)';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ssss', $id_prodi, $kode_prodi, $nama_prodi, $id_jurusan);
    
    if ($stmt->execute()) {
        $message = 'Data prodi berhasil ditambahkan!';
        $messageType = 'success';
        // Clear form data
        $id_prodi = $kode_prodi = $nama_prodi = $id_jurusan = '';
    } else {
        $message = 'Gagal menambahkan data prodi: ' . $stmt->error;
        $messageType = 'error';
    }
    $stmt->close();
}
?>

<main class="dashboard-main">
    <div class="edit-mahasiswa-card">
        <header><h1 style="color: white;">Tambah Prodi</h1></header>
        
        <?php if ($message): ?>
            <div class="message <?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <form method="post" class="edit-mahasiswa-form" style="display:flex;flex-direction:column;gap:1.2em;">
            <label>ID Prodi
                <input type="text" name="id_prodi" value="<?= htmlspecialchars($id_prodi ?? '') ?>" required>
            </label>
            
            <label>Kode Prodi
                <input type="text" name="kode_prodi" value="<?= htmlspecialchars($kode_prodi ?? '') ?>" required>
            </label>
            
            <label>Nama Prodi
                <input type="text" name="nama_prodi" value="<?= htmlspecialchars($nama_prodi ?? '') ?>" required>
            </label>
            
            <label>Jurusan
                <select name="id_jurusan" required>
                    <option value="">Pilih Jurusan</option>
                    <?php foreach ($jurusanList as $j): ?>
                        <option value="<?= htmlspecialchars($j['id_jurusan']) ?>" <?= (isset($id_jurusan) && $id_jurusan == $j['id_jurusan']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($j['nama_jurusan']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </label>
            
            <button type="submit">Tambah Prodi</button>
            <a href="index.php?page=prodiPage.php" class="cancel-btn">Batal</a>
        </form>
    </div>
</main>

<style>
.message {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
</style>
