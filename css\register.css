/* Register Page Styling */
.register-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-secondary);
    padding: var(--spacing-md);
}

.register-container {
    width: 100%;
    max-width: 600px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-2xl);
}

.register-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.register-header h1 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.75rem;
    color: var(--text-color);
    font-weight: 600;
}

.register-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.register-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.register-form .form-group {
    display: flex;
    flex-direction: column;
}

.register-form .form-group.full-width {
    grid-column: 1 / -1;
}

.register-form label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.register-form input,
.register-form select {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.register-form input:focus,
.register-form select:focus {
    border-color: var(--border-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.register-form input[type="file"] {
    padding: var(--spacing-sm);
    border: 2px dashed var(--border-color);
    background-color: var(--background-secondary);
    cursor: pointer;
}

.register-form input[type="file"]:hover {
    border-color: var(--border-hover);
    background-color: var(--background-tertiary);
}

.register-form input[type="file"]:focus {
    border-color: var(--border-focus);
    border-style: solid;
}

.register-form small {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
}

.register-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.register-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.register-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.register-button:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.register-cancel {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.register-cancel:hover {
    background: var(--secondary-hover);
    transform: translateY(-1px);
}

.register-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.register-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.register-footer a:hover {
    text-decoration: underline;
}

.register-error {
    background: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
    grid-column: 1 / -1;
}

.register-success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
    grid-column: 1 / -1;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
}

.password-strength.weak {
    color: var(--error-color);
}

.password-strength.medium {
    color: var(--warning-color);
}

.password-strength.strong {
    color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 600px) {
    .register-container {
        padding: var(--spacing-xl);
        margin: var(--spacing-sm);
    }

    .register-form {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .register-actions {
        flex-direction: column;
    }

    .register-button,
    .register-cancel {
        width: 100%;
    }

    .register-header h1 {
        font-size: 1.5rem;
    }
}