<?php
include 'database.php';

$id = isset($_GET['id']) ? $_GET['id'] : '';
$prodi = null;
$jurusanList = [];

if ($id) {
    // Fetch prodi
    $stmt = $conn->prepare('SELECT id_prodi, kode_prodi, nama_prodi, id_jurusan FROM prodi WHERE id_prodi = ?');
    $stmt->bind_param('s', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $row = $result->fetch_assoc()) {
        $prodi = $row;
    }
    $stmt->close();

    // Fetch jurusan
    $result = $conn->query('SELECT id_jurusan, nama_jurusan FROM jurusan');
    while ($row = $result->fetch_assoc()) {
        $jurusanList[] = $row;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = $_POST['id_prodi'];
    $kode = $_POST['kode_prodi'];
    $nama = $_POST['nama_prodi'];
    $jurusan = $_POST['id_jurusan'];

    // Debug: Log POST data
    error_log('FORM POST DATA: ' . print_r($_POST, true));

    // Direct update instead of CURL
    $sql = 'UPDATE prodi SET kode_prodi=?, nama_prodi=?, id_jurusan=? WHERE id_prodi=?';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ssss', $kode, $nama, $jurusan, $id);

    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo "<script>alert('Data berhasil diperbarui!');window.location='index.php?page=prodiPage.php';</script>";
            exit;
        } else {
            echo "<script>alert('Tidak ada perubahan data atau ID tidak ditemukan.');</script>";
        }
    } else {
        error_log('MYSQL ERROR: ' . $stmt->error);
        echo "<script>alert('Gagal memperbarui data: " . addslashes($stmt->error) . "');</script>";
    }
    $stmt->close();
}
?>
<main class="dashboard-main">
    <div class="edit-mahasiswa-card">
        <header><h1 style="color: white;">Edit Prodi</h1></header>
        <?php if ($prodi): ?>
        <form method="post" class="edit-mahasiswa-form" style="display:flex;flex-direction:column;gap:1.2em;">
            <label>ID Prodi
                <input type="text" name="id_prodi" value="<?= htmlspecialchars($prodi['id_prodi']) ?>" readonly style="background:#f1f1f1;cursor:not-allowed;">
            </label>
            <label>Kode Prodi
                <input type="text" name="kode_prodi" value="<?= htmlspecialchars($prodi['kode_prodi']) ?>" required>
            </label>
            <label>Nama Prodi
                <input type="text" name="nama_prodi" value="<?= htmlspecialchars($prodi['nama_prodi']) ?>" required>
            </label>
            <label>Jurusan
                <select name="id_jurusan" required>
                    <?php foreach ($jurusanList as $j): ?>
                        <option value="<?= htmlspecialchars($j['id_jurusan']) ?>" <?= $prodi['id_jurusan'] == $j['id_jurusan'] ? 'selected' : '' ?>><?= htmlspecialchars($j['nama_jurusan']) ?></option>
                    <?php endforeach; ?>
                </select>
            </label>
            <button type="submit">Simpan</button>
            <a href="index.php?page=prodiPage.php" class="cancel-btn">Batal</a>
        </form>
        <?php else: ?>
            <p class="error-message">Data prodi tidak ditemukan.</p>
        <?php endif; ?>
    </div>
</main>
