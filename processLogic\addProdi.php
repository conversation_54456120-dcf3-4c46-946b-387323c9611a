<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_prodi = isset($_POST['id_prodi']) ? $_POST['id_prodi'] : '';
    $kode_prodi = isset($_POST['kode_prodi']) ? $_POST['kode_prodi'] : '';
    $nama_prodi = isset($_POST['nama_prodi']) ? $_POST['nama_prodi'] : '';
    $id_jurusan = isset($_POST['id_jurusan']) ? $_POST['id_jurusan'] : '';

    // Validate required fields
    if (empty($id_prodi) || empty($kode_prodi) || empty($nama_prodi) || empty($id_jurusan)) {
        echo json_encode(['success' => false, 'message' => 'Semua field wajib diisi.']);
        exit;
    }

    // Insert into database
    $sql = 'INSERT INTO prodi (id_prodi, kode_prodi, nama_prodi, id_jurusan) VALUES (?, ?, ?, ?)';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ssss', $id_prodi, $kode_prodi, $nama_prodi, $id_jurusan);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Data prodi berhasil ditambahkan.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menambahkan data prodi: ' . $stmt->error]);
    }
    $stmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
?>
