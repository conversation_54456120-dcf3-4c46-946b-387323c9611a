/* General Form Styling */
.form-container {
    max-width: 500px;
    margin: var(--spacing-xl) auto;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-xl);
}

.form-container header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.form-container header h1,
.form-container header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color);
    font-weight: 600;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-grid.single-column {
    grid-template-columns: 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--border-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-group small {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
}

/* File Input Styling */
.form-group input[type="file"] {
    padding: var(--spacing-sm);
    border: 2px dashed var(--border-color);
    background-color: var(--background-secondary);
    cursor: pointer;
}

.form-group input[type="file"]:hover {
    border-color: var(--border-hover);
    background-color: var(--background-tertiary);
}

.form-group input[type="file"]:focus {
    border-color: var(--border-focus);
    border-style: solid;
}

/* Button Styling */
.form-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.form-button {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.form-button.primary {
    background: var(--primary-color);
    color: white;
}

.form-button.primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.form-button.secondary {
    background: var(--secondary-color);
    color: white;
}

.form-button.secondary:hover {
    background: var(--secondary-hover);
    transform: translateY(-1px);
}

/* Error States */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: var(--error-color);
}

.form-group.error label {
    color: var(--error-color);
}

.error-text {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
}

/* Success States */
.form-group.success input,
.form-group.success select,
.form-group.success textarea {
    border-color: var(--success-color);
}

/* Responsive Design */
@media (max-width: 600px) {
    .form-container {
        margin: var(--spacing-md) var(--spacing-sm);
        padding: var(--spacing-lg);
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .form-actions {
        flex-direction: column;
    }

    .form-button {
        width: 100%;
    }
}