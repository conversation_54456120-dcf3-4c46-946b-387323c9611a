<?php
session_start();

$login_alert = null;
if (isset($_SESSION['login_alert'])) {
    $login_alert = $_SESSION['login_alert'];
    unset($_SESSION['login_alert']);
}

if (isset($_SESSION['user_id']) && isset($_SESSION['user_email'])) {
    header('Location: index.php');
    exit();
}

include 'database.php';

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');

    if (empty($email) || empty($password)) {
        $error_message = 'Email dan password harus diisi.';
    } else {
        $sql = "SELECT nim_mahasiswa, nama_mahasiswa, email_mahasiswa, password_mahasiswa, prodi_mahasiswa, jurusan_mahasiswa, foto_mahasiswa
                FROM mahasiswa
                WHERE email_mahasiswa = ?";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param('s', $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();

            if ($password === $user['password_mahasiswa']) {
                $_SESSION['user_id'] = $user['nim_mahasiswa'];
                $_SESSION['user_name'] = $user['nama_mahasiswa'];
                $_SESSION['user_email'] = $user['email_mahasiswa'];
                $_SESSION['user_prodi'] = $user['prodi_mahasiswa'];
                $_SESSION['user_jurusan'] = $user['jurusan_mahasiswa'];
                $_SESSION['user_foto'] = $user['foto_mahasiswa'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();

                header('Location: index.php');
                exit();
            } else {
                $error_message = 'Email atau password salah.';
            }
        } else {
            $error_message = 'Email atau password salah.';
        }

        $stmt->close();
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Uji Kompetensi JWD 2025</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-header">
            <h1>Login</h1>
            <p>Masuk ke Dashboard Manajemen Mahasiswa JWD 2025</p>
        </div>

        <?php if (!empty($error_message)): ?>
            <div class="login-error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
            <div class="login-success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <form class="login-form" method="POST" action="login.php">
            <div class="form-group">
                <label for="email">Email</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value="<?php echo htmlspecialchars($email ?? ''); ?>"
                    placeholder="Masukkan email Anda"
                >
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    required
                    placeholder="Masukkan password Anda"
                >
            </div>

            <div class="remember-me">
                <input type="checkbox" id="remember" name="remember">
                <label for="remember">Ingat saya</label>
            </div>

            <button type="submit" class="login-button">
                Masuk
            </button>
        </form>

        <div class="login-footer">
            <p>Belum punya akun? Hubungi administrator untuk mendapatkan akses.</p>
        </div>
    </div>

    <script>
        // Simple form validation
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!email || !password) {
                e.preventDefault();
                alert('Email dan password harus diisi.');
                return false;
            }

            // Disable submit button to prevent double submission
            const submitBtn = document.querySelector('.login-button');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Memproses...';
        });

        // Auto-focus on email field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
    </script>

<?php if ($login_alert): ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            title: '<?= addslashes($login_alert['title']) ?>',
            text: '<?= addslashes($login_alert['text']) ?>',
            icon: '<?= $login_alert['icon'] ?>',
            confirmButtonText: 'OK'
        });
    });
</script>
<?php endif; ?>
</body>
</html>