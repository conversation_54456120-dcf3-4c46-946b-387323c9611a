@import url('root.css');

@import url('login.css');
@import url('register.css');
@import url('forms.css');

@import url('dataTables.css');

/* --- Dashboard Styles for index.php --- */
body {
    background: var(--background-color);
    color: var(--text-color);
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    font-size: 14px;
}

header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xl) 0;
    text-align: center;
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    margin: 0 0 var(--spacing-lg) 0;
    font-size: 1.75rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    color: white;
}

nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

nav ul li a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    font-size: 0.9rem;
    display: block;
}

nav ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* --- Persistent Active Link Styling for Header Navigation --- */
nav ul li a.active {
    background: white;
    color: var(--primary-color) !important;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

/* --- Dashboard Grid Layout --- */
main.dashboard-main {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    min-height: 60vh;
}

main {
    padding: 0 var(--spacing-md);
    max-width: 1400px;
    margin: 0 auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-md);
}

thead th, tbody td {
    text-align: center;
    vertical-align: middle;
}

.table-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.table-container.full-width {
    grid-column: 1 / -1;
    width: 100%;
    max-width: 100%;
    margin-bottom: var(--spacing-xl);
}

.table-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table-container header {
    background: var(--background-secondary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.table-container header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.table-container > div[style*="overflow-x"] {
    padding: 0;
}

@media (max-width: 900px) {
    .table-row {
        grid-template-columns: 1fr;
    }

    main.dashboard-main,
    main {
        padding: 0 var(--spacing-sm);
    }
}

footer {
    background: var(--background-secondary);
    color: var(--text-secondary);
    text-align: center;
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-2xl);
    font-size: 0.875rem;
    border-top: 1px solid var(--border-color);
}

/* Utility Classes */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1.5;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: var(--secondary-hover);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
}