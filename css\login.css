/* Login Page Styling */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-secondary);
    padding: var(--spacing-md);
}

.login-container {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-2xl);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-header h1 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.75rem;
    color: var(--text-color);
    font-weight: 600;
}

.login-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.login-form .form-group {
    display: flex;
    flex-direction: column;
}

.login-form label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.login-form input {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.login-form input:focus {
    border-color: var(--border-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.login-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: var(--spacing-sm);
}

.login-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.login-button:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.login-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.login-footer a:hover {
    text-decoration: underline;
}

.login-error {
    background: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
}

.login-success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
}

/* Remember Me Checkbox */
.remember-me {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-sm) 0;
}

.remember-me input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.remember-me label {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
}

/* Login Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Container Animation */
.login-container {
    animation: fadeInScale 0.6s ease-out;
}

.login-header {
    animation: slideInDown 0.8s ease-out;
}

/* Form Input Animations */
.form-group input {
    transition: all 0.3s ease;
    position: relative;
}

.form-group input:focus {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.form-group {
    position: relative;
    animation: slideInDown 0.6s ease-out;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }

/* Password Validation Styles */
.password-requirements {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--background-tertiary);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--warning-color);
    font-size: 0.75rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.password-requirements.show {
    opacity: 1;
    transform: translateY(0);
}

.password-requirements ul {
    margin: 0;
    padding-left: var(--spacing-md);
    list-style: none;
}

.password-requirements li {
    margin: var(--spacing-xs) 0;
    position: relative;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.password-requirements li::before {
    content: "✗";
    position: absolute;
    left: -16px;
    color: var(--error-color);
    font-weight: bold;
    transition: all 0.3s ease;
}

.password-requirements li.valid {
    color: var(--success-color);
}

.password-requirements li.valid::before {
    content: "✓";
    color: var(--success-color);
}

/* Email Validation */
.email-validation {
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

.email-validation.show {
    opacity: 1;
    transform: translateY(0);
}

.email-validation.valid {
    color: var(--success-color);
}

.email-validation.invalid {
    color: var(--error-color);
}

/* Form Group States */
.form-group.valid input {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-group.invalid input {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    animation: shake 0.5s ease-in-out;
}

/* Button Animations */
.login-button {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-button:active {
    transform: translateY(0);
}

.login-button.loading {
    pointer-events: none;
    opacity: 0.8;
}

.login-button.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message Animation */
.login-error, .login-success {
    animation: slideInDown 0.5s ease-out;
}

.login-error.shake {
    animation: shake 0.5s ease-in-out;
}

/* Remember Me Animation */
.remember-me {
    animation: slideInDown 0.6s ease-out;
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

/* Footer Animation */
.login-footer {
    animation: slideInDown 0.6s ease-out;
    animation-delay: 0.5s;
    animation-fill-mode: both;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: var(--spacing-xl);
        margin: var(--spacing-sm);
    }

    .login-header h1 {
        font-size: 1.5rem;
    }

    .password-requirements {
        font-size: 0.7rem;
    }
}