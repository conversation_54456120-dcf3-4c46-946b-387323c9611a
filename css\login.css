/* Login Page Styling */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-secondary);
    padding: var(--spacing-md);
}

.login-container {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-2xl);
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-header h1 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.75rem;
    color: var(--text-color);
    font-weight: 600;
}

.login-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.login-form .form-group {
    display: flex;
    flex-direction: column;
}

.login-form label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    font-size: 0.875rem;
}

.login-form input {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.login-form input:focus {
    border-color: var(--border-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.login-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: var(--spacing-sm);
}

.login-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.login-button:disabled {
    background: var(--secondary-color);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.login-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.login-footer a:hover {
    text-decoration: underline;
}

.login-error {
    background: var(--error-light);
    color: var(--error-color);
    border: 1px solid var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
}

.login-success {
    background: var(--success-light);
    color: var(--success-color);
    border: 1px solid var(--success-color);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-lg);
}

/* Remember Me Checkbox */
.remember-me {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-sm) 0;
}

.remember-me input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.remember-me label {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: var(--spacing-xl);
        margin: var(--spacing-sm);
    }

    .login-header h1 {
        font-size: 1.5rem;
    }
}