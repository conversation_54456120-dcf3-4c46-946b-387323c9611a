<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nim = isset($_POST['nim_mahasiswa']) ? $_POST['nim_mahasiswa'] : null;
    if ($nim) {
        $stmt = $conn->prepare('DELETE FROM mahasiswa WHERE nim_mahasiswa = ?');
        $stmt->bind_param('s', $nim);
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Data mahasiswa berhasil dihapus.']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Gagal menghapus data mahasiswa.']);
        }
        $stmt->close();
    } else {
        echo json_encode(['success' => false, 'message' => 'NIM tidak ditemukan.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
