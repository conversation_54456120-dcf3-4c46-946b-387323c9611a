<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nim = isset($_POST['nim_mahasiswa']) ? $_POST['nim_mahasiswa'] : '';
    $nama = isset($_POST['nama_mahasiswa']) ? $_POST['nama_mahasiswa'] : '';
    $prodi = isset($_POST['prodi_mahasiswa']) ? $_POST['prodi_mahasiswa'] : '';
    $jurusan = isset($_POST['jurusan_mahasiswa']) ? $_POST['jurusan_mahasiswa'] : '';
    $email = isset($_POST['email_mahasiswa']) ? $_POST['email_mahasiswa'] : '';
    $password = isset($_POST['password_mahasiswa']) ? $_POST['password_mahasiswa'] : '';

    // Validate required fields
    if (empty($nim) || empty($nama) || empty($prodi) || empty($jurusan) || empty($email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Semua field wajib diisi.']);
        exit;
    }

    // Handle file upload
    $foto_path = 'images/mahasiswa_picture/default.jpg'; // Default photo

    if (isset($_FILES['foto_mahasiswa']) && $_FILES['foto_mahasiswa']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'images/mahasiswa_picture/';
        $file_extension = strtolower(pathinfo($_FILES['foto_mahasiswa']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($file_extension, $allowed_extensions)) {
            $new_filename = $nim . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;

            if (move_uploaded_file($_FILES['foto_mahasiswa']['tmp_name'], $upload_path)) {
                $foto_path = 'images/mahasiswa_picture/' . $new_filename;
            }
        }
    }

    // Insert into database
    $sql = 'INSERT INTO mahasiswa (nim_mahasiswa, nama_mahasiswa, prodi_mahasiswa, jurusan_mahasiswa, email_mahasiswa, password_mahasiswa, foto_mahasiswa) VALUES (?, ?, ?, ?, ?, ?, ?)';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sssssss', $nim, $nama, $prodi, $jurusan, $email, $password, $foto_path);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Data mahasiswa berhasil ditambahkan.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menambahkan data mahasiswa: ' . $stmt->error]);
    }
    $stmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
?>
