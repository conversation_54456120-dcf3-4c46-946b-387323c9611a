<?php include 'database.php'; ?>
    <main class="dashboard-main">
        <div class="table-container full-width">
            <header>
                <h2>Data Mahasiswa</h2>
            </header>
            <div style="overflow-x:auto;">
            <table class="display" id="mahasiswaTable">
                <thead>
                    <tr>
                        <th>NIM</th>
                        <th><PERSON>a</th>
                        <th>Prodi</th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th>Email</th>
                        <th>Foto</th>
                    </tr>
                </thead>
                <tbody>
                <?php
                $sql = "SELECT m.nim_mahasiswa, m.nama_mahasiswa, p.nama_prodi, j.nama_jurusan, m.email_mahasiswa, m.foto_mahasiswa FROM mahasiswa m JOIN prodi p ON m.prodi_mahasiswa = p.id_prodi JOIN jurusan j ON m.jurusan_mahasiswa = j.id_jurusan";
                $result = $conn->query($sql);
                if ($result && $result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['nim_mahasiswa']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['nama_mahasiswa']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['nama_prodi']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['nama_jurusan']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['email_mahasiswa']) . "</td>";
                        echo "<td><img src='" . htmlspecialchars($row['foto_mahasiswa']) . "' alt='Foto' class='profile-photo'></td>";
                        echo "</tr>";
                    }
                } else {
                    echo "<tr><td colspan='6'>No data</td></tr>";
                }
                ?>
                </tbody>
            </table>
            </div>
        </div>
        <div class="table-row">
            <div class="table-container">
                <header>
                    <h2>Data Prodi</h2>
                </header>
                <div style="overflow-x:auto;">
                <table class="display" id="prodiTable">
                    <thead>
                        <tr>
                            <th>Kode Prodi</th>
                            <th>Nama Prodi</th>
                            <th>Jurusan</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                    $sql = "SELECT p.kode_prodi, p.nama_prodi, j.nama_jurusan FROM prodi p JOIN jurusan j ON p.id_jurusan = j.id_jurusan";
                    $result = $conn->query($sql);
                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($row['kode_prodi']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['nama_prodi']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['nama_jurusan']) . "</td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='3'>No data</td></tr>";
                    }
                    ?>
                    </tbody>
                </table>
                </div>
            </div>
            <div class="table-container">
                <header>
                    <h2>Data Jurusan</h2>
                </header>
                <div style="overflow-x:auto;">
                <table class="display" id="jurusanTable">
                    <thead>
                        <tr>
                            <th>Kode Jurusan</th>
                            <th>Nama Jurusan</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                    $sql = "SELECT kode_jurusan, nama_jurusan FROM jurusan";
                    $result = $conn->query($sql);
                    if ($result && $result->num_rows > 0) {
                        while($row = $result->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($row['kode_jurusan']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['nama_jurusan']) . "</td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='2'>No data</td></tr>";
                    }
                    ?>
                    </tbody>
                </table>
                </div>
            </div>
        </div>
    </main>