<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_jurusan = isset($_POST['id_jurusan']) ? $_POST['id_jurusan'] : '';
    $kode_jurusan = isset($_POST['kode_jurusan']) ? $_POST['kode_jurusan'] : '';
    $nama_jurusan = isset($_POST['nama_jurusan']) ? $_POST['nama_jurusan'] : '';

    // Validate required fields
    if (empty($id_jurusan) || empty($kode_jurusan) || empty($nama_jurusan)) {
        echo json_encode(['success' => false, 'message' => 'Semua field wajib diisi.']);
        exit;
    }

    // Insert into database
    $sql = 'INSERT INTO jurusan (id_jurusan, kode_jurusan, nama_jurusan) VALUES (?, ?, ?)';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sss', $id_jurusan, $kode_jurusan, $nama_jurusan);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Data jurusan berhasil ditambahkan.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menambahkan data jurusan: ' . $stmt->error]);
    }
    $stmt->close();
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
?>
