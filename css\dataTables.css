/* Enhanced DataTables Styling */
.dataTables_wrapper {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
}

/* Table Header Controls */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: var(--spacing-lg);
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-top: var(--spacing-lg);
}

.dataTables_wrapper .dataTables_length label,
.dataTables_wrapper .dataTables_filter label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
    display: inline-block;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_filter input[type="search"] {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin-left: var(--spacing-sm);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    width: 250px;
    background: white;
}

.dataTables_wrapper .dataTables_filter input[type="search"]:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-sm);
    font-size: 0.875rem;
    background-color: white;
    transition: all 0.2s ease;
}

.dataTables_wrapper .dataTables_length select:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Table Styling */
table.dataTable {
    width: 100% !important;
    border-collapse: separate;
    border-spacing: 0;
    background-color: white;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
    margin: 0 !important;
    border: none;
}

table.dataTable thead th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    text-align: center;
    padding: var(--spacing-md) var(--spacing-sm);
    border: none;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

table.dataTable thead th:first-child {
    border-top-left-radius: 0;
}

table.dataTable thead th:last-child {
    border-top-right-radius: 0;
}

table.dataTable tbody td {
    padding: var(--spacing-md) var(--spacing-sm);
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-color);
    transition: background-color 0.2s ease;
}

table.dataTable tbody tr {
    transition: background-color 0.2s ease;
}

table.dataTable tbody tr:hover {
    background-color: var(--background-secondary);
}

table.dataTable tbody tr:nth-child(even) {
    background-color: var(--background-tertiary);
}

table.dataTable tbody tr:nth-child(even):hover {
    background-color: var(--background-secondary);
}

/* Action Buttons */
.btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-xs);
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-size: 0.75rem;
}

.btn-edit {
    background-color: var(--success-color);
    color: white;
}

.btn-edit:hover {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
    background-color: var(--error-color);
    color: white;
}

.btn-delete:hover, .btn-delete-prodi:hover, .btn-delete-jurusan:hover {
    background-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Add Button Styling */
.btn-add {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.2s ease;
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm);
}

.btn-add:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

.btn-add::before {
    content: '+';
    font-size: 1.1rem;
    font-weight: bold;
}

/* Pagination Styling */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-xs);
    background-color: white;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    font-weight: 600;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Info Text Styling */
.dataTables_wrapper .dataTables_info {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dataTables_wrapper .dataTables_filter input[type="search"] {
        width: 200px;
    }

    table.dataTable thead th,
    table.dataTable tbody td {
        padding: var(--spacing-sm);
        font-size: 0.75rem;
    }

    .btn-edit, .btn-delete, .btn-delete-prodi, .btn-delete-jurusan {
        width: 28px;
        height: 28px;
    }

    .dataTables_wrapper {
        padding: var(--spacing-md);
    }
}

/* Photo styling in tables */
.profile-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
    transition: all 0.2s ease;
}

.profile-photo:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}