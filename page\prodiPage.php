<?php include 'database.php'; ?>
<main class="dashboard-main">
    <div class="table-container full-width">
        <header>
            <h2>Data Prodi</h2>
            <a href="index.php?page=addProdi.php" class="btn-add">Tambah Prodi</a>
        </header>
        <div style="overflow-x:auto;">
        <table class="display" id="prodiTable">
            <thead>
                <tr>
                    <th>ID Prodi</th>
                    <th>Kode Prodi</th>
                    <th><PERSON><PERSON> Prodi</th>
                    <th><PERSON><PERSON><PERSON></th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
            <?php
            $sql = "SELECT p.id_prodi, p.kode_prodi, p.nama_prodi, j.nama_jurusan FROM prodi p JOIN jurusan j ON p.id_jurusan = j.id_jurusan";
            $result = $conn->query($sql);
            if ($result && $result->num_rows > 0) {
                while($row = $result->fetch_assoc()) {
                    echo "<tr data-id='" . htmlspecialchars($row['id_prodi']) . "'>";
                    echo "<td>" . htmlspecialchars($row['id_prodi']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['kode_prodi']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nama_prodi']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nama_jurusan']) . "</td>";
                    echo "<td>"
                        . "<a href='index.php?page=editProdi.php&id=" . urlencode($row['id_prodi']) . "' class='btn-edit' title='Edit'><img src='images/assets/edit.png' alt='Edit' style='width:16px;height:16px;vertical-align:middle;'></a> "
                        . "<a href='#' class='btn-delete-prodi' title='Delete'><img src='images/assets/eraser.png' alt='Delete' style='width:16px;height:16px;vertical-align:middle;'></a>"
                        . "</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr><td colspan='5'>No data</td></tr>";
            }
            ?>
            </tbody>
        </table>
        </div>
    </div>
</main>

<script>
$(document).ready(function() {
    // Delete prodi functionality
    $('.btn-delete-prodi').click(function(e) {
        e.preventDefault();
        const row = $(this).closest('tr');
        const id = row.data('id');
        const nama = row.find('td:nth-child(3)').text();

        if (confirm('Apakah Anda yakin ingin menghapus prodi "' + nama + '"?')) {
            $.ajax({
                url: 'processLogic/deleteProdi.php',
                type: 'POST',
                data: { id_prodi: id },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('Data prodi berhasil dihapus!');
                        location.reload();
                    } else {
                        alert('Gagal menghapus data: ' + response.message);
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat menghapus data.');
                }
            });
        }
    });
});
</script>
