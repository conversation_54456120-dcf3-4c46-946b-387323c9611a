<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug: Log all POST data
    error_log('POST DATA: ' . print_r($_POST, true));

    $id = isset($_POST['id_prodi']) ? $_POST['id_prodi'] : null;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'ID Prodi tidak ditemukan.']);
        $conn->close();
        exit;
    }

    // If update flag is set, perform update
    if (isset($_POST['update'])) {
        $kode = isset($_POST['kode_prodi']) ? $_POST['kode_prodi'] : '';
        $nama = isset($_POST['nama_prodi']) ? $_POST['nama_prodi'] : '';
        $jurusan = isset($_POST['id_jurusan']) ? $_POST['id_jurusan'] : '';

        $sql = 'UPDATE prodi SET kode_prodi=?, nama_prodi=?, id_jurusan=? WHERE id_prodi=?';
        error_log('SQL: ' . $sql);
        error_log('PARAMS: ' . print_r([$kode, $nama, $jurusan, $id], true));

        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ssss', $kode, $nama, $jurusan, $id);

        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Data prodi berhasil diperbarui.']);
        } else {
            error_log('MYSQL ERROR: ' . $stmt->error);
            echo json_encode(['success' => false, 'message' => 'Gagal memperbarui data prodi.', 'error' => $stmt->error]);
        }
        $stmt->close();
    } else {
        // Otherwise, return current data + jurusan
        $stmt = $conn->prepare('SELECT id_prodi, kode_prodi, nama_prodi, id_jurusan FROM prodi WHERE id_prodi = ?');
        $stmt->bind_param('s', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $prodi = $result && $row = $result->fetch_assoc() ? $row : null;
        $stmt->close();

        // Fetch all jurusan
        $jurusan = [];
        $result = $conn->query('SELECT id_jurusan, nama_jurusan FROM jurusan');
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $jurusan[] = $row;
            }
        }

        if ($prodi) {
            echo json_encode(['success' => true, 'data' => $prodi, 'jurusan' => $jurusan]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Data prodi tidak ditemukan.']);
        }
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();
