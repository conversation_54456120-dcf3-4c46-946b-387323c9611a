<?php
header('Content-Type: application/json');
include 'database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug: Log all POST data
    error_log('POST DATA: ' . print_r($_POST, true));
    $nim = isset($_POST['nim_mahasiswa']) ? $_POST['nim_mahasiswa'] : null;
    if (!$nim) {
        echo json_encode(['success' => false, 'message' => 'NIM tidak ditemukan.']);
        $conn->close();
        exit;
    }

    // If update flag is set, perform update
    if (isset($_POST['update'])) {
        $nama = isset($_POST['nama_mahasiswa']) ? $_POST['nama_mahasiswa'] : '';
        $prodi = isset($_POST['prodi_mahasiswa']) ? $_POST['prodi_mahasiswa'] : '';
        $jurusan = isset($_POST['jurusan_mahasiswa']) ? $_POST['jurusan_mahasiswa'] : '';
        $email = isset($_POST['email_mahasiswa']) ? $_POST['email_mahasiswa'] : '';
        $password = isset($_POST['password_mahasiswa']) ? $_POST['password_mahasiswa'] : '';

        // Get current photo path
        $stmt_photo = $conn->prepare('SELECT foto_mahasiswa FROM mahasiswa WHERE nim_mahasiswa = ?');
        $stmt_photo->bind_param('s', $nim);
        $stmt_photo->execute();
        $result_photo = $stmt_photo->get_result();
        $current_photo = $result_photo->fetch_assoc()['foto_mahasiswa'] ?? 'images/mahasiswa_picture/default.jpg';
        $stmt_photo->close();

        // Handle file upload
        $foto_path = $current_photo; // Keep existing photo by default

        if (isset($_FILES['foto_mahasiswa']) && $_FILES['foto_mahasiswa']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'images/mahasiswa_picture/';
            $file_extension = strtolower(pathinfo($_FILES['foto_mahasiswa']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

            if (in_array($file_extension, $allowed_extensions)) {
                $new_filename = $nim . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;

                if (move_uploaded_file($_FILES['foto_mahasiswa']['tmp_name'], $upload_path)) {
                    // Delete old photo if it's not the default
                    if ($current_photo !== 'images/mahasiswa_picture/default.jpg' && file_exists($current_photo)) {
                        unlink($current_photo);
                    }
                    $foto_path = $upload_path;
                }
            }
        }

        if ($password !== '') {
            $sql = 'UPDATE mahasiswa SET nama_mahasiswa=?, prodi_mahasiswa=?, jurusan_mahasiswa=?, email_mahasiswa=?, password_mahasiswa=?, foto_mahasiswa=? WHERE nim_mahasiswa=?';
            error_log('SQL: ' . $sql);
            error_log('PARAMS: ' . print_r([$nama, $prodi, $jurusan, $email, $password, $foto_path, $nim], true));
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('sssssss', $nama, $prodi, $jurusan, $email, $password, $foto_path, $nim);
        } else {
            $sql = 'UPDATE mahasiswa SET nama_mahasiswa=?, prodi_mahasiswa=?, jurusan_mahasiswa=?, email_mahasiswa=?, foto_mahasiswa=? WHERE nim_mahasiswa=?';
            error_log('SQL: ' . $sql);
            error_log('PARAMS: ' . print_r([$nama, $prodi, $jurusan, $email, $foto_path, $nim], true));
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssssss', $nama, $prodi, $jurusan, $email, $foto_path, $nim);
        }
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Data mahasiswa berhasil diperbarui.']);
        } else {
            error_log('MYSQL ERROR: ' . $stmt->error);
            echo json_encode(['success' => false, 'message' => 'Gagal memperbarui data mahasiswa.', 'error' => $stmt->error]);
        }
        $stmt->close();
    } else {
        // Otherwise, return current data + prodi + jurusan
        $stmt = $conn->prepare('SELECT nim_mahasiswa, nama_mahasiswa, prodi_mahasiswa, jurusan_mahasiswa, email_mahasiswa FROM mahasiswa WHERE nim_mahasiswa = ?');
        $stmt->bind_param('s', $nim);
        $stmt->execute();
        $result = $stmt->get_result();
        $mahasiswa = $result && $row = $result->fetch_assoc() ? $row : null;
        $stmt->close();
        // Fetch all prodi
        $prodi = [];
        $result = $conn->query('SELECT id_prodi, nama_prodi FROM prodi');
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $prodi[] = $row;
            }
        }
        // Fetch all jurusan
        $jurusan = [];
        $result = $conn->query('SELECT id_jurusan, nama_jurusan FROM jurusan');
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $jurusan[] = $row;
            }
        }
        if ($mahasiswa) {
            echo json_encode(['success' => true, 'data' => $mahasiswa, 'prodi' => $prodi, 'jurusan' => $jurusan]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Data mahasiswa tidak ditemukan.']);
        }
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Metode tidak diizinkan.']);
}
$conn->close();